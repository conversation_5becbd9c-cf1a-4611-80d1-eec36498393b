# 获取准确的指数成分股数据
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
import time

# 初始化pro接口
pro = ts.pro_api('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')

def get_constituents_for_date(index_code, trade_date):
    """
    获取指定日期的指数成分股
    trade_date: 格式为 YYYYMMDD 的字符串
    """
    try:
        # 将日期转换为该月的第一天和最后一天
        date_obj = datetime.strptime(trade_date, "%Y%m%d")
        start_date = date_obj.replace(day=1)
        
        # 获取该月最后一天
        if date_obj.month == 12:
            next_month = date_obj.replace(year=date_obj.year + 1, month=1, day=1)
        else:
            next_month = date_obj.replace(month=date_obj.month + 1, day=1)
        end_date = next_month - timedelta(days=1)
        
        start_date_int = int(start_date.strftime("%Y%m%d"))
        end_date_int = int(end_date.strftime("%Y%m%d"))
        
        print(f"获取 {index_code} 在 {trade_date} 的成分股 (查询范围: {start_date_int} - {end_date_int})")
        
        # 调用API
        df = pro.index_weight(**{
            "index_code": index_code,
            "trade_date": "",
            "start_date": start_date_int,
            "end_date": end_date_int,
            "ts_code": "",
            "limit": "",
            "offset": ""
        }, fields=[
            "index_code",
            "con_code", 
            "trade_date"
        ])
        
        if not df.empty:
            # 如果有多个交易日的数据，选择最接近目标日期的
            df['trade_date'] = pd.to_datetime(df['trade_date'], format='%Y%m%d')
            target_date = pd.to_datetime(trade_date, format='%Y%m%d')
            
            # 找到最接近目标日期的交易日
            df['date_diff'] = abs((df['trade_date'] - target_date).dt.days)
            closest_date = df.loc[df['date_diff'].idxmin(), 'trade_date']
            
            # 筛选该日期的数据
            result_df = df[df['trade_date'] == closest_date].copy()
            
            print(f"找到最接近日期: {closest_date.strftime('%Y%m%d')}, 成分股数量: {len(result_df)}")
            
            return result_df
        else:
            print(f"未找到 {index_code} 在 {trade_date} 的成分股数据")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"获取成分股失败: {e}")
        return pd.DataFrame()

def convert_stock_code_to_format(ts_code):
    """
    将股票代码转换为要求的格式
    例: 600000.SH -> sh600000, 000001.SZ -> sz000001
    """
    if '.' in ts_code:
        code, exchange = ts_code.split('.')
        if exchange == 'SZ':
            return f"sz{code}"
        elif exchange == 'SH':
            return f"sh{code}"
        elif exchange == 'BJ':
            return f"bj{code}"
        else:
            return f"{exchange.lower()}{code}"
    else:
        return ts_code.lower()

def get_formatted_constituents(index_code, trade_date):
    """
    获取格式化的成分股字符串
    """
    df = get_constituents_for_date(index_code, trade_date)
    
    if df.empty:
        return ""
    
    # 转换股票代码格式
    stock_codes = []
    unique_codes = df['con_code'].dropna().unique()
    
    for code in unique_codes:
        formatted_code = convert_stock_code_to_format(str(code))
        stock_codes.append(formatted_code)
    
    # 排序并返回格式化的字符串
    stock_codes.sort()
    result = ' '.join(stock_codes)
    
    print(f"格式化后的成分股 ({len(stock_codes)} 只): {result[:100]}...")
    
    return result

def test_multiple_dates():
    """
    测试多个日期的成分股获取
    """
    test_cases = [
        ("000300.SH", "20240101"),  # 沪深300 2024年1月
        ("000300.SH", "20240601"),  # 沪深300 2024年6月
        ("000300.SH", "20241201"),  # 沪深300 2024年12月
        ("000016.SH", "20240601"),  # 上证50 2024年6月
        ("932000.CSI", "20240601"), # 中证2000 2024年6月
    ]
    
    print("=" * 80)
    print("测试多个日期的成分股获取")
    print("=" * 80)
    
    for index_code, trade_date in test_cases:
        print(f"\n{'='*60}")
        print(f"测试: {index_code} - {trade_date}")
        print(f"{'='*60}")
        
        constituents = get_formatted_constituents(index_code, trade_date)
        
        if constituents:
            print(f"✅ 成功获取成分股")
            print(f"📊 成分股数量: {len(constituents.split())} 只")
        else:
            print(f"❌ 未获取到成分股")
        
        time.sleep(1)  # 避免API限制

def test_time_series():
    """
    测试时间序列的成分股变化
    """
    index_code = "000300.SH"  # 沪深300
    dates = [
        "20230101", "20230601", "20231201",
        "20240101", "20240601", "20241201"
    ]
    
    print("=" * 80)
    print(f"测试 {index_code} 的时间序列成分股变化")
    print("=" * 80)
    
    results = {}
    
    for date in dates:
        print(f"\n处理日期: {date}")
        constituents = get_formatted_constituents(index_code, date)
        
        if constituents:
            stock_list = constituents.split()
            results[date] = set(stock_list)
            print(f"✅ {date}: {len(stock_list)} 只股票")
        else:
            results[date] = set()
            print(f"❌ {date}: 无数据")
        
        time.sleep(1)
    
    # 分析变化
    print(f"\n📊 成分股变化分析:")
    prev_date = None
    for date in dates:
        if prev_date and results[date] and results[prev_date]:
            added = results[date] - results[prev_date]
            removed = results[prev_date] - results[date]
            
            print(f"{prev_date} -> {date}:")
            print(f"  新增: {len(added)} 只")
            print(f"  移除: {len(removed)} 只")
            if added:
                print(f"  新增股票: {', '.join(list(added)[:5])}...")
            if removed:
                print(f"  移除股票: {', '.join(list(removed)[:5])}...")
        
        prev_date = date

if __name__ == "__main__":
    print("🚀 开始获取准确的指数成分股数据")
    
    # 选择测试模式
    print("\n选择测试模式:")
    print("1. 测试多个日期的成分股获取")
    print("2. 测试时间序列的成分股变化")
    print("3. 测试单个指定日期")
    
    # 默认运行模式1
    mode = 1
    
    if mode == 1:
        test_multiple_dates()
    elif mode == 2:
        test_time_series()
    else:
        # 测试单个指定日期
        index_code = "000300.SH"
        trade_date = "20241101"
        
        print(f"\n测试单个日期: {index_code} - {trade_date}")
        constituents = get_formatted_constituents(index_code, trade_date)
        
        if constituents:
            print(f"✅ 成功获取成分股")
            print(f"📊 完整结果:\n{constituents}")
        else:
            print(f"❌ 未获取到成分股")
    
    print(f"\n🎉 测试完成!")
