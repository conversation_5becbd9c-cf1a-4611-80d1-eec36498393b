# 股票详细数据获取工具
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
import time
import os
import sys
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue
import random

# 初始化pro接口
pro = ts.pro_api('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')

# 尝试导入xtquant（用于获取5分钟数据）
try:
    from xtquant import xtdata
    XTQUANT_AVAILABLE = True
    print("✅ xtquant库可用，支持5分钟数据获取")
except ImportError:
    XTQUANT_AVAILABLE = False
    print("⚠️  xtquant库不可用，将跳过5分钟数据获取")

# 月度成分股缓存，按月份缓存成分股数据
monthly_constituents_cache = {}

# API限制处理配置
API_RETRY_TIMES = 3  # 重试次数
API_RETRY_DELAY = 5  # 重试延迟（秒）
API_RATE_LIMIT_DELAY = 1  # API调用间隔（秒）
THREAD_POOL_SIZE = 5  # 线程池大小

# 线程锁，用于API调用限制
api_lock = threading.Lock()

def api_call_with_retry(api_func, *args, max_retries=API_RETRY_TIMES, **kwargs):
    """
    带重试机制的API调用
    """
    for retry in range(max_retries):
        try:
            with api_lock:
                # 添加API调用间隔
                time.sleep(API_RATE_LIMIT_DELAY + random.uniform(0, 0.5))
                result = api_func(*args, **kwargs)
                return result
        except Exception as e:
            error_msg = str(e).lower()

            # 检查是否是API限制错误
            if any(keyword in error_msg for keyword in ['limit', 'rate', 'quota', 'exceed', '限制', '超出']):
                wait_time = API_RETRY_DELAY * (retry + 1) + random.uniform(1, 3)
                print(f"   ⚠️  API限制，等待 {wait_time:.1f} 秒后重试 (第{retry+1}次)")
                time.sleep(wait_time)
            elif retry < max_retries - 1:
                wait_time = 2 ** retry + random.uniform(0, 1)
                print(f"   ⚠️  API调用失败，等待 {wait_time:.1f} 秒后重试 (第{retry+1}次): {e}")
                time.sleep(wait_time)
            else:
                print(f"   ❌ API调用最终失败: {e}")
                raise e

    return None

def get_index_constituents_for_month(index_code, trade_date):
    """
    获取指定月份的指数成分股
    """
    global monthly_constituents_cache

    try:
        # 提取年月作为缓存键
        month_key = trade_date[:6]  # YYYYMM
        cache_key = f"{index_code}_{month_key}"

        # 检查缓存
        if cache_key in monthly_constituents_cache:
            return monthly_constituents_cache[cache_key]

        # 计算该月的第一天和最后一天
        from datetime import datetime, timedelta
        date_obj = datetime.strptime(trade_date, "%Y%m%d")
        start_date = date_obj.replace(day=1)

        if date_obj.month == 12:
            next_month = date_obj.replace(year=date_obj.year + 1, month=1, day=1)
        else:
            next_month = date_obj.replace(month=date_obj.month + 1, day=1)
        end_date = next_month - timedelta(days=1)

        start_date_int = int(start_date.strftime("%Y%m%d"))
        end_date_int = int(end_date.strftime("%Y%m%d"))

        print(f"   📋 获取 {index_code} 在 {month_key} 的成分股...")

        # 调用API
        df_constituents = pro.index_weight(**{
            "index_code": index_code,
            "trade_date": "",
            "start_date": start_date_int,
            "end_date": end_date_int,
            "ts_code": "",
            "limit": "",
            "offset": ""
        }, fields=[
            "index_code",
            "con_code",
            "trade_date"
        ])

        if not df_constituents.empty:
            # 转换股票代码格式
            def convert_stock_code_to_format(ts_code):
                if '.' in ts_code:
                    code, exchange = ts_code.split('.')
                    if exchange == 'SZ':
                        return f"sz{code}"
                    elif exchange == 'SH':
                        return f"sh{code}"
                    elif exchange == 'BJ':
                        return f"bj{code}"
                    else:
                        return f"{exchange.lower()}{code}"
                else:
                    return ts_code.lower()

            stock_codes = []
            unique_codes = df_constituents['con_code'].dropna().unique()

            for code in unique_codes:
                formatted_code = convert_stock_code_to_format(str(code))
                stock_codes.append(formatted_code)

            stock_codes.sort()
            result = ' '.join(stock_codes)

            # 存入缓存
            monthly_constituents_cache[cache_key] = result
            print(f"   ✅ 获取到 {len(stock_codes)} 只成分股")

            return result
        else:
            print(f"   ⚠️  {index_code} 在 {month_key} 无成分股数据")
            monthly_constituents_cache[cache_key] = ""
            return ""

    except Exception as e:
        print(f"   ❌ 获取 {index_code} 成分股失败: {e}")
        monthly_constituents_cache[cache_key] = ""
        return ""

def get_5min_close_prices(ts_code, trade_date):
    """
    获取指定股票在指定交易日的5分钟收盘价数据
    返回09:35、09:45、09:55的收盘价
    """
    if not XTQUANT_AVAILABLE:
        return {'09:35收盘价': 0, '09:45收盘价': 0, '09:55收盘价': 0}

    try:
        print(f"   📊 获取 {ts_code} 在 {trade_date} 的5分钟数据...")

        # 转换股票代码格式（tushare格式转xtquant格式）
        if ts_code.endswith('.SH'):
            xt_code = ts_code.replace('.SH', '.SH')
        elif ts_code.endswith('.SZ'):
            xt_code = ts_code.replace('.SZ', '.SZ')
        else:
            xt_code = ts_code

        # 获取5分钟数据
        data = xtdata.get_local_data(
            field_list=['time', 'close'],
            stock_list=[xt_code],
            period='5m',
            dividend_type='front'
        )

        if xt_code not in data or data[xt_code].empty:
            print(f"   ⚠️  {ts_code} 无5分钟数据")
            return {'09:35收盘价': 0, '09:45收盘价': 0, '09:55收盘价': 0}

        df = data[xt_code]

        # 转换时间戳为datetime
        df['datetime'] = df['time'].apply(lambda x: datetime.fromtimestamp(x/1000.0))
        df['time_str'] = df['datetime'].dt.strftime('%H:%M:%S')
        df['date'] = df['datetime'].dt.date

        # 筛选指定交易日的数据
        target_date = datetime.strptime(trade_date, "%Y%m%d").date()
        df_day = df[df['date'] == target_date]

        if df_day.empty:
            print(f"   ⚠️  {ts_code} 在 {trade_date} 无5分钟数据")
            return {'09:35收盘价': 0, '09:45收盘价': 0, '09:55收盘价': 0}

        # 获取特定时间点的收盘价
        result = {'09:35收盘价': 0, '09:45收盘价': 0, '09:55收盘价': 0}

        target_times = {
            '09:35:00': '09:35收盘价',
            '09:45:00': '09:45收盘价',
            '09:55:00': '09:55收盘价'
        }

        for time_str, column_name in target_times.items():
            time_data = df_day[df_day['time_str'] == time_str]
            if not time_data.empty:
                result[column_name] = float(time_data.iloc[0]['close'])
                print(f"   ✅ {time_str}: {result[column_name]}")
            else:
                print(f"   ⚠️  {time_str}: 无数据")

        return result

    except Exception as e:
        print(f"   ❌ 获取 {ts_code} 5分钟数据失败: {e}")
        return {'09:35收盘价': 0, '09:45收盘价': 0, '09:55收盘价': 0}

def get_stock_basic_info():
    """
    获取股票基本信息
    """
    try:
        print("📋 获取股票基本信息...")
        df_basic = pro.stock_basic(exchange='', list_status='L', fields=[
            'ts_code', 'symbol', 'name', 'area', 'industry', 'market', 'list_date'
        ])
        print(f"✅ 获取到 {len(df_basic)} 只股票基本信息")
        return df_basic
    except Exception as e:
        print(f"❌ 获取股票基本信息失败: {e}")
        return pd.DataFrame()

def get_stock_daily_data(ts_code, start_date, end_date):
    """
    获取股票日线数据
    """
    try:
        df_daily = pro.daily(**{
            "ts_code": ts_code,
            "trade_date": "",
            "start_date": start_date,
            "end_date": end_date
        }, fields=[
            "ts_code", "trade_date", "open", "high", "low", "close", "pre_close", 
            "change", "pct_chg", "vol", "amount"
        ])
        return df_daily
    except Exception as e:
        print(f"❌ 获取 {ts_code} 日线数据失败: {e}")
        return pd.DataFrame()

def get_stock_daily_basic(ts_code, start_date, end_date):
    """
    获取股票每日基本面数据
    """
    try:
        df_basic = pro.daily_basic(**{
            "ts_code": ts_code,
            "trade_date": "",
            "start_date": start_date,
            "end_date": end_date
        }, fields=[
            "ts_code", "trade_date", "close", "turnover_rate", "turnover_rate_f",
            "volume_ratio", "pe", "pe_ttm", "pb", "ps", "ps_ttm", "dv_ratio", "dv_ttm",
            "total_share", "float_share", "free_share", "total_mv", "circ_mv"
        ])
        return df_basic
    except Exception as e:
        print(f"❌ 获取 {ts_code} 每日基本面数据失败: {e}")
        return pd.DataFrame()

def get_stock_financial_data(ts_code, start_date, end_date):
    """
    获取股票财务数据（扩大时间范围以便计算TTM）
    """
    try:
        # 扩大查询范围，获取更多历史数据用于TTM计算
        from datetime import datetime, timedelta
        start_obj = datetime.strptime(start_date, "%Y%m%d")
        # 向前推2年，确保有足够数据计算TTM
        extended_start = start_obj - timedelta(days=730)
        extended_start_str = extended_start.strftime("%Y%m%d")

        print(f"   📊 获取财务数据范围: {extended_start_str} 到 {end_date}")

        # 获取利润表数据（使用单季报表计算TTM）
        df_income = pro.income(**{
            "ts_code": ts_code,
            "start_date": extended_start_str,
            "end_date": end_date,
            "report_type": "2"  # 单季合并报表
        }, fields=[
            "ts_code", "end_date", "n_income", "n_income_attr_p", "report_type"
        ])

        # 获取现金流量表数据（使用单季报表计算TTM）
        df_cashflow = pro.cashflow(**{
            "ts_code": ts_code,
            "start_date": extended_start_str,
            "end_date": end_date,
            "report_type": "2"  # 单季合并报表
        }, fields=[
            "ts_code", "end_date", "n_cashflow_act", "report_type"
        ])

        # 获取资产负债表数据
        df_balancesheet = pro.balancesheet(**{
            "ts_code": ts_code,
            "start_date": extended_start_str,
            "end_date": end_date,
            "report_type": "1"
        }, fields=[
            "ts_code", "end_date", "total_assets", "total_liab", "total_hldr_eqy_exc_min_int", "report_type"
        ])

        return df_income, df_cashflow, df_balancesheet
    except Exception as e:
        print(f"❌ 获取 {ts_code} 财务数据失败: {e}")
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()

def calculate_ttm_value(df_financial, value_field, trade_date_str):
    """
    计算TTM值
    TTM = 最近4个季度的单季数据相加
    """
    try:
        if df_financial.empty:
            print(f"   ⚠️  财务数据为空，无法计算TTM")
            return 0

        # 按报告期排序（降序，最新的在前）
        df_sorted = df_financial.sort_values('end_date', ascending=False)

        # 将交易日期转换为年月
        from datetime import datetime
        trade_date = datetime.strptime(trade_date_str, "%Y%m%d")

        print(f"   📊 计算TTM - 交易日期: {trade_date_str}")

        # 查找交易日期之前的最近4个季度数据
        recent_quarters = []

        for _, row in df_sorted.iterrows():
            end_date = str(row['end_date'])
            if len(end_date) != 8:
                continue

            end_month = int(end_date[4:6])
            end_day = int(end_date[6:8])

            # 确定是否为季度末（只接受季度末数据）
            if not ((end_month == 3 and end_day == 31) or
                    (end_month == 6 and end_day == 30) or
                    (end_month == 9 and end_day == 30) or
                    (end_month == 12 and end_day == 31)):
                continue

            # 检查是否在交易日期之前
            report_date = datetime.strptime(end_date, "%Y%m%d")
            if report_date <= trade_date:
                recent_quarters.append(row)
                print(f"   ✅ 找到季度数据: {end_date}, 值: {row.get(value_field, 0)}")

                # 找到4个季度就停止
                if len(recent_quarters) >= 4:
                    break

        # 计算TTM（最近4个季度相加）
        if len(recent_quarters) >= 4:
            ttm_value = 0
            print(f"   📊 TTM计算（最近4个季度相加）:")

            for i, quarter_data in enumerate(recent_quarters):
                value = float(quarter_data.get(value_field, 0) or 0)
                ttm_value += value
                print(f"      Q{i+1}: {quarter_data['end_date']} = {value}")

            print(f"   📊 TTM总计: {ttm_value}")
            return ttm_value
        else:
            print(f"   ⚠️  TTM计算数据不足，只找到 {len(recent_quarters)} 个季度")

            # 如果数据不足4个季度，使用现有数据
            if recent_quarters:
                ttm_value = sum(float(q.get(value_field, 0) or 0) for q in recent_quarters)
                print(f"   📊 使用现有 {len(recent_quarters)} 个季度数据: {ttm_value}")
                return ttm_value
            else:
                print(f"   ❌ 无可用财务数据")
                return 0

    except Exception as e:
        print(f"   ❌ 计算TTM失败: {e}")
        import traceback
        print(f"   详细错误: {traceback.format_exc()}")
        return 0

def get_stock_moneyflow(ts_code, start_date, end_date):
    """
    获取股票资金流向数据
    """
    try:
        df_moneyflow = pro.moneyflow(**{
            "ts_code": ts_code,
            "trade_date": "",
            "start_date": start_date,
            "end_date": end_date
        }, fields=[
            "ts_code", "trade_date", "buy_sm_vol", "buy_sm_amount", "sell_sm_vol", "sell_sm_amount",
            "buy_md_vol", "buy_md_amount", "sell_md_vol", "sell_md_amount",
            "buy_lg_vol", "buy_lg_amount", "sell_lg_vol", "sell_lg_amount",
            "buy_elg_vol", "buy_elg_amount", "sell_elg_vol", "sell_elg_amount",
            "net_mf_vol", "net_mf_amount"
        ])
        return df_moneyflow
    except Exception as e:
        print(f"❌ 获取 {ts_code} 资金流向数据失败: {e}")
        return pd.DataFrame()

def check_index_membership(ts_code, trade_date):
    """
    检查股票是否为各指数成分股
    归属则返回1，不归属则返回空字符串
    """
    memberships = {
        '沪深300成分股': '',
        '上证50成分股': '',
        '中证500成分股': '',
        '中证1000成分股': '',
        '中证2000成分股': '',
        '创业板指成分股': ''
    }

    # 指数代码映射
    index_mapping = {
        '000300.SH': '沪深300成分股',
        '000016.SH': '上证50成分股',
        '000905.SH': '中证500成分股',
        '000852.SH': '中证1000成分股',
        '932000.CSI': '中证2000成分股',
        '399006.SZ': '创业板指成分股'
    }

    try:
        # 转换股票代码格式用于比较
        def convert_stock_code_to_format(ts_code):
            if '.' in ts_code:
                code, exchange = ts_code.split('.')
                if exchange == 'SZ':
                    return f"sz{code}"
                elif exchange == 'SH':
                    return f"sh{code}"
                elif exchange == 'BJ':
                    return f"bj{code}"
                else:
                    return f"{exchange.lower()}{code}"
            else:
                return ts_code.lower()

        formatted_stock_code = convert_stock_code_to_format(ts_code)

        # 检查每个指数
        for index_code, membership_key in index_mapping.items():
            try:
                # 获取该月份的成分股
                constituents_str = get_index_constituents_for_month(index_code, trade_date)

                if constituents_str and formatted_stock_code in constituents_str.split():
                    memberships[membership_key] = '1'

            except Exception as e:
                # 单个指数检查失败不影响其他指数
                print(f"   ⚠️  检查 {index_code} 成分股失败: {e}")
                continue

        return memberships

    except Exception as e:
        print(f"   ⚠️  检查 {ts_code} 指数成分股失败: {e}")
        return memberships

def get_stock_industry_info(ts_code):
    """
    获取股票申万行业分类信息
    使用index_member_all接口获取完整的申万三级行业分类
    """
    try:
        print(f"   🏭 获取 {ts_code} 申万行业分类...")

        # 使用index_member_all接口获取申万行业分类
        df_industry = pro.index_member_all(**{
            "l1_code": "",
            "l2_code": "",
            "l3_code": "",
            "ts_code": ts_code,
            "is_new": "Y"
        }, fields=[
            "l1_code", "l1_name", "l2_code", "l2_name",
            "l3_code", "l3_name", "ts_code", "name"
        ])

        if not df_industry.empty:
            # 获取最新的行业分类（第一条记录）
            industry_row = df_industry.iloc[0]
            return {
                '新版申万一级行业名称': industry_row.get('l1_name', ''),
                '新版申万二级行业名称': industry_row.get('l2_name', ''),
                '新版申万三级行业名称': industry_row.get('l3_name', '')
            }
        else:
            print(f"   ⚠️  {ts_code} 未找到申万行业分类")
            return {
                '新版申万一级行业名称': '',
                '新版申万二级行业名称': '',
                '新版申万三级行业名称': ''
            }
    except Exception as e:
        print(f"   ❌ 获取 {ts_code} 行业信息失败: {e}")
        return {
            '新版申万一级行业名称': '',
            '新版申万二级行业名称': '',
            '新版申万三级行业名称': ''
        }

def collect_single_stock_data(ts_code, stock_name, start_date, end_date):
    """
    收集单只股票的完整数据
    """
    print(f"📊 开始收集 {ts_code} - {stock_name} 的数据...")
    
    all_data = []
    
    try:
        # 1. 获取日线数据
        print(f"   📈 获取日线数据...")
        df_daily = api_call_with_retry(get_stock_daily_data, ts_code, start_date, end_date)
        
        if df_daily.empty:
            print(f"   ⚠️  {ts_code} 无日线数据")
            return pd.DataFrame()
        
        # 2. 获取每日基本面数据
        print(f"   📊 获取基本面数据...")
        df_basic = api_call_with_retry(get_stock_daily_basic, ts_code, start_date, end_date)

        # 3. 获取资金流向数据
        print(f"   💰 获取资金流向数据...")
        df_moneyflow = api_call_with_retry(get_stock_moneyflow, ts_code, start_date, end_date)

        # 4. 获取财务数据
        print(f"   📋 获取财务数据...")
        df_income, df_cashflow, df_balancesheet = api_call_with_retry(get_stock_financial_data, ts_code, start_date, end_date)
        
        # 5. 获取行业信息
        print(f"   🏭 获取行业信息...")
        industry_info = get_stock_industry_info(ts_code)
        
        # 6. 合并数据
        print(f"   🔄 合并数据...")

        # 预处理财务数据，按日期排序
        if not df_income.empty:
            df_income = df_income.sort_values('end_date', ascending=False)
        if not df_cashflow.empty:
            df_cashflow = df_cashflow.sort_values('end_date', ascending=False)
        if not df_balancesheet.empty:
            df_balancesheet = df_balancesheet.sort_values('end_date', ascending=False)

        for _, row in df_daily.iterrows():
            trade_date = row['trade_date']

            # 基础数据
            data_row = {
                '股票代码': ts_code,
                '股票名称': stock_name,
                '交易日期': trade_date,
                '开盘价': row.get('open', 0),
                '最高价': row.get('high', 0),
                '最低价': row.get('low', 0),
                '收盘价': row.get('close', 0),
                '前收盘价': row.get('pre_close', 0),
                '成交量': row.get('vol', 0),
                '成交额': row.get('amount', 0),
            }

            # 添加基本面数据
            basic_row = df_basic[df_basic['trade_date'] == trade_date]
            if not basic_row.empty:
                data_row.update({
                    '流通市值': basic_row.iloc[0].get('circ_mv', 0),
                    '总市值': basic_row.iloc[0].get('total_mv', 0),
                })
            else:
                data_row.update({
                    '流通市值': 0,
                    '总市值': 0,
                })

            # 添加财务数据（查找交易日期之前最近的财务数据）
            def get_latest_financial_data(df_financial, trade_date_str):
                """获取交易日期之前最近的财务数据"""
                if df_financial.empty:
                    return None

                # 将交易日期转换为可比较的格式
                trade_date_int = int(trade_date_str)

                # 查找交易日期之前最近的财务数据
                for _, fin_row in df_financial.iterrows():
                    fin_date = fin_row['end_date']
                    if isinstance(fin_date, str):
                        fin_date_int = int(fin_date)
                    else:
                        fin_date_int = int(str(fin_date))

                    if fin_date_int <= trade_date_int:
                        return fin_row

                # 如果没找到之前的数据，返回最早的数据
                return df_financial.iloc[-1] if not df_financial.empty else None

            # 计算TTM数据
            net_income_ttm = calculate_ttm_value(df_income, 'n_income_attr_p', trade_date)
            cashflow_ttm = calculate_ttm_value(df_cashflow, 'n_cashflow_act', trade_date)

            # 获取最近的资产负债表数据和当季利润数据
            latest_income = get_latest_financial_data(df_income, trade_date)
            latest_balance = get_latest_financial_data(df_balancesheet, trade_date)

            data_row.update({
                '净利润TTM': net_income_ttm,
                '现金流TTM': cashflow_ttm,
                '净资产': latest_balance.get('total_hldr_eqy_exc_min_int', 0) if latest_balance is not None else 0,
                '总资产': latest_balance.get('total_assets', 0) if latest_balance is not None else 0,
                '总负债': latest_balance.get('total_liab', 0) if latest_balance is not None else 0,
                '净利润(当季)': latest_income.get('n_income', 0) if latest_income is not None else 0,
            })
            
            # 添加资金流向数据
            moneyflow_row = df_moneyflow[df_moneyflow['trade_date'] == trade_date]
            if not moneyflow_row.empty:
                data_row.update({
                    '中户资金买入额': moneyflow_row.iloc[0].get('buy_md_amount', 0),
                    '中户资金卖出额': moneyflow_row.iloc[0].get('sell_md_amount', 0),
                    '大户资金买入额': moneyflow_row.iloc[0].get('buy_lg_amount', 0),
                    '大户资金卖出额': moneyflow_row.iloc[0].get('sell_lg_amount', 0),
                    '散户资金买入额': moneyflow_row.iloc[0].get('buy_sm_amount', 0),
                    '散户资金卖出额': moneyflow_row.iloc[0].get('sell_sm_amount', 0),
                    '机构资金买入额': moneyflow_row.iloc[0].get('buy_elg_amount', 0),
                    '机构资金卖出额': moneyflow_row.iloc[0].get('sell_elg_amount', 0),
                })
            else:
                data_row.update({
                    '中户资金买入额': 0,
                    '中户资金卖出额': 0,
                    '大户资金买入额': 0,
                    '大户资金卖出额': 0,
                    '散户资金买入额': 0,
                    '散户资金卖出额': 0,
                    '机构资金买入额': 0,
                    '机构资金卖出额': 0,
                })
            
            # 添加指数成分股信息
            memberships = check_index_membership(ts_code, trade_date)
            data_row.update(memberships)

            # 添加行业信息
            data_row.update(industry_info)

            # 添加5分钟收盘价数据
            min5_prices = get_5min_close_prices(ts_code, trade_date)
            data_row.update(min5_prices)

            all_data.append(data_row)
        
        result_df = pd.DataFrame(all_data)

        # 按交易日期排序（从早到晚）
        if not result_df.empty and '交易日期' in result_df.columns:
            result_df = result_df.sort_values('交易日期', ascending=True)
            result_df = result_df.reset_index(drop=True)
            print(f"   ✅ {ts_code} 数据收集完成，共 {len(result_df)} 条记录")
            print(f"   📅 日期范围: {result_df['交易日期'].min()} 到 {result_df['交易日期'].max()}")
        else:
            print(f"   ✅ {ts_code} 数据收集完成，共 {len(result_df)} 条记录")

        return result_df

    except Exception as e:
        print(f"   ❌ {ts_code} 数据收集失败: {e}")
        return pd.DataFrame()

def collect_multiple_stocks_data_separate_files(stock_list, start_date, end_date, max_workers=THREAD_POOL_SIZE):
    """
    多线程批量收集股票数据，每只股票单独保存为CSV文件
    """
    print(f"🚀 开始多线程收集 {len(stock_list)} 只股票的数据...")
    print(f"📊 线程池大小: {max_workers}")
    print(f"⏱️  预计耗时: {len(stock_list) * 2 / max_workers / 60:.1f} 分钟")
    print(f"💾 每只股票将单独保存为CSV文件")

    # 创建输出目录
    output_dir = "stock_detailed_data"
    os.makedirs(output_dir, exist_ok=True)

    completed_count = 0
    failed_count = 0
    saved_files = []

    def process_and_save_stock(stock):
        """处理单只股票并保存文件"""
        try:
            result_df = collect_single_stock_data(stock['ts_code'], stock['name'], start_date, end_date)

            if not result_df.empty:
                # 生成文件名：股票代码格式转换
                # 例：000001.SZ -> sz000001.csv, 600000.SH -> sh600000.csv
                ts_code = stock['ts_code']
                if ts_code.endswith('.SZ'):
                    filename = f"sz{ts_code[:6]}.csv"
                elif ts_code.endswith('.SH'):
                    filename = f"sh{ts_code[:6]}.csv"
                elif ts_code.endswith('.BJ'):
                    filename = f"bj{ts_code[:6]}.csv"
                else:
                    filename = f"{ts_code.replace('.', '_')}.csv"

                filepath = os.path.join(output_dir, filename)

                # 保存文件
                result_df.to_csv(filepath, index=False, encoding='utf-8-sig')

                file_size = os.path.getsize(filepath) / 1024  # KB
                return {
                    'status': 'success',
                    'ts_code': ts_code,
                    'name': stock['name'],
                    'filename': filename,
                    'filepath': filepath,
                    'records': len(result_df),
                    'size_kb': file_size
                }
            else:
                return {
                    'status': 'failed',
                    'ts_code': ts_code,
                    'name': stock['name'],
                    'error': '无数据'
                }

        except Exception as e:
            return {
                'status': 'error',
                'ts_code': stock['ts_code'],
                'name': stock['name'],
                'error': str(e)
            }

    # 使用线程池执行
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_stock = {}
        for _, stock in stock_list.iterrows():
            future = executor.submit(process_and_save_stock, stock)
            future_to_stock[future] = stock

        # 收集结果
        for future in as_completed(future_to_stock):
            stock = future_to_stock[future]
            try:
                result = future.result()

                if result['status'] == 'success':
                    completed_count += 1
                    saved_files.append(result)
                    print(f"✅ 完成 {result['ts_code']} - {result['name']} -> {result['filename']} "
                          f"({result['records']}条记录, {result['size_kb']:.1f}KB) "
                          f"({completed_count}/{len(stock_list)})")
                else:
                    failed_count += 1
                    print(f"❌ 失败 {result['ts_code']} - {result['name']}: {result['error']} "
                          f"({failed_count} 个失败)")

            except Exception as e:
                failed_count += 1
                print(f"❌ 异常 {stock['ts_code']} - {stock['name']}: {e}")

            # 显示进度
            total_processed = completed_count + failed_count
            if total_processed % 10 == 0:
                progress = total_processed / len(stock_list) * 100
                print(f"📊 进度: {progress:.1f}% ({total_processed}/{len(stock_list)})")

    print(f"\n🎉 批量收集完成!")
    print(f"✅ 成功: {completed_count} 只")
    print(f"❌ 失败: {failed_count} 只")
    print(f"📊 成功率: {completed_count/(completed_count+failed_count)*100:.1f}%")

    if saved_files:
        total_records = sum(f['records'] for f in saved_files)
        total_size = sum(f['size_kb'] for f in saved_files)
        print(f"📋 总记录数: {total_records}")
        print(f"💾 总文件大小: {total_size/1024:.1f} MB")
        print(f"📁 保存目录: {output_dir}")

        # 显示前几个文件示例
        print(f"📄 生成的文件示例:")
        for i, file_info in enumerate(saved_files[:5]):
            print(f"   {i+1}. {file_info['filename']} ({file_info['records']}条记录)")
        if len(saved_files) > 5:
            print(f"   ... 还有 {len(saved_files)-5} 个文件")

        return saved_files
    else:
        print(f"❌ 无有效数据")
        return []

def main():
    """
    主函数：股票数据收集
    """
    import sys
    
    # 解析命令行参数
    start_date = '20250425'  # 默认值
    if len(sys.argv) > 1:
        start_date = sys.argv[1]
        print(f"📅 使用命令行指定的开始日期: {start_date}")
    else:
        # 交互式输入日期
        print(f"📅 当前默认开始日期: {start_date}")
        user_input = input("请输入开始日期 (格式: YYYYMMDD，直接回车使用默认值): ").strip()
        
        if user_input:
            start_date = user_input
            print(f"📅 使用您输入的开始日期: {start_date}")
        else:
            print(f"📅 使用默认开始日期: {start_date}")
    
    # 验证日期格式
    try:
        from datetime import datetime
        datetime.strptime(start_date, "%Y%m%d")
        print(f"✅ 日期格式验证通过: {start_date}")
    except ValueError:
        print(f"❌ 日期格式错误: {start_date}")
        print(f"💡 正确格式: YYYYMMDD，例如: 20240101")
        sys.exit(1)
    
    # 计算结束日期（今天）
    end_date = datetime.now().strftime("%Y%m%d")
    
    print("=" * 80)
    print("🚀 开始收集股票详细数据")
    print(f"📅 开始日期: {start_date}")
    print(f"📅 结束日期: {end_date}")
    print("=" * 80)
    
    # 获取股票基本信息
    df_stocks = get_stock_basic_info()
    if df_stocks.empty:
        print("❌ 无法获取股票基本信息，程序退出")
        return
    
    print(f"\n请选择运行模式:")
    print(f"1. 收集单只股票数据")
    print(f"2. 收集前10只股票数据（测试）")
    print(f"3. 收集所有股票数据")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    if choice == "1":
        # 单只股票
        stock_code = input("请输入股票代码 (如: 000001.SZ): ").strip()
        stock_info = df_stocks[df_stocks['ts_code'] == stock_code]
        
        if stock_info.empty:
            print(f"❌ 未找到股票代码: {stock_code}")
            return
        
        stock_name = stock_info.iloc[0]['name']
        print(f"\n🎯 开始收集 {stock_code} - {stock_name} 的数据")
        
        df_result = collect_single_stock_data(stock_code, stock_name, start_date, end_date)
        
        if not df_result.empty:
            # 保存数据
            output_dir = "stock_detailed_data"
            os.makedirs(output_dir, exist_ok=True)
            filename = f"{stock_code.replace('.', '_')}_detailed_data.csv"
            filepath = os.path.join(output_dir, filename)
            df_result.to_csv(filepath, index=False, encoding='utf-8-sig')
            
            print(f"✅ 数据收集完成:")
            print(f"   - 记录数: {len(df_result)}")
            print(f"   - 保存路径: {filepath}")
        else:
            print(f"❌ 数据收集失败")
    
    elif choice == "2":
        # 前10只股票（测试）
        print(f"\n🎯 开始收集前10只股票的数据（测试模式）")
        test_stocks = df_stocks.head(10)

        print(f"📊 测试股票列表:")
        for i, (_, stock) in enumerate(test_stocks.iterrows(), 1):
            print(f"   {i}. {stock['ts_code']} - {stock['name']}")

        start_time = time.time()
        saved_files = collect_multiple_stocks_data_separate_files(test_stocks, start_date, end_date, max_workers=3)

        end_time = time.time()
        elapsed_time = end_time - start_time

        if saved_files:
            print(f"✅ 测试数据收集完成:")
            print(f"   - 股票数: {len(test_stocks)}")
            print(f"   - 成功文件数: {len(saved_files)}")
            print(f"   - 耗时: {elapsed_time/60:.1f} 分钟")
            print(f"   - 平均每只股票: {elapsed_time/len(test_stocks):.1f} 秒")
        else:
            print(f"❌ 测试数据收集失败")
    
    elif choice == "3":
        # 所有股票
        print(f"\n🎯 开始收集所有股票的数据")
        print(f"📊 总股票数: {len(df_stocks)}")
        print(f"⚠️  这将需要很长时间，建议先测试少量股票")

        # 询问线程数
        thread_input = input(f"请输入线程数 (1-20，默认{THREAD_POOL_SIZE}): ").strip()
        if thread_input.isdigit():
            max_workers = min(max(int(thread_input), 1), 20)
        else:
            max_workers = THREAD_POOL_SIZE

        print(f"📊 使用线程数: {max_workers}")
        print(f"⏱️  预计耗时: {len(df_stocks) * 2 / max_workers / 60:.1f} 分钟")

        confirm = input("确认要收集所有股票数据吗？(y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 用户取消操作")
            return

        print(f"\n🚀 开始多线程批量收集...")
        start_time = time.time()

        saved_files = collect_multiple_stocks_data_separate_files(df_stocks, start_date, end_date, max_workers)

        end_time = time.time()
        elapsed_time = end_time - start_time

        if saved_files:
            print(f"✅ 所有股票数据收集完成:")
            print(f"   - 股票数: {len(df_stocks)}")
            print(f"   - 成功文件数: {len(saved_files)}")
            print(f"   - 耗时: {elapsed_time/60:.1f} 分钟")
            print(f"   - 平均每只股票: {elapsed_time/len(df_stocks):.1f} 秒")
        else:
            print(f"❌ 批量数据收集失败")
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
