# 测试净利润TTM计算
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta

# 初始化pro接口
pro = ts.pro_api('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')

def test_net_income_ttm():
    """
    测试平安银行净利润TTM计算
    """
    ts_code = "000001.SZ"  # 平安银行
    
    print("🔍 测试平安银行净利润TTM计算")
    print("=" * 60)
    
    try:
        # 获取利润表数据（单季报表）
        print("📊 获取利润表数据（单季报表）...")
        df_income = pro.income(**{
            "ts_code": ts_code,
            "start_date": "20220101",
            "end_date": "20241231",
            "report_type": "2"  # 单季合并报表
        }, fields=[
            "ts_code", "end_date", "n_income", "n_income_attr_p", "report_type"
        ])
        
        if df_income.empty:
            print("❌ 未获取到利润表数据")
            return
        
        print(f"✅ 获取到 {len(df_income)} 条利润表数据")
        print("\n📋 净利润数据详情:")
        df_sorted = df_income.sort_values('end_date', ascending=False)
        
        for _, row in df_sorted.iterrows():
            end_date = row['end_date']
            net_income = row['n_income_attr_p']  # 归属母公司净利润
            print(f"  {end_date}: {net_income:,.0f} (原始值)")
            print(f"  {end_date}: {net_income/100000000:.2f} 亿元")
        
        # 计算2024年Q2的净利润TTM（最近4个季度相加）
        print(f"\n🧮 计算2024年Q2的净利润TTM:")
        print(f"方法: 最近4个季度的单季净利润相加")
        
        # 查找2024年Q2之前的最近4个季度
        trade_date = datetime.strptime("20240630", "%Y%m%d")
        recent_quarters = []
        
        for _, row in df_sorted.iterrows():
            end_date = str(row['end_date'])
            if len(end_date) != 8:
                continue
                
            end_month = int(end_date[4:6])
            end_day = int(end_date[6:8])
            
            # 确定是否为季度末
            if not ((end_month == 3 and end_day == 31) or
                    (end_month == 6 and end_day == 30) or
                    (end_month == 9 and end_day == 30) or
                    (end_month == 12 and end_day == 31)):
                continue
            
            # 检查是否在交易日期之前
            report_date = datetime.strptime(end_date, "%Y%m%d")
            if report_date <= trade_date:
                recent_quarters.append(row)
                print(f"  ✅ Q{len(recent_quarters)}: {end_date} = {row['n_income_attr_p']:,.0f} ({row['n_income_attr_p']/100000000:.2f}亿元)")
                
                # 找到4个季度就停止
                if len(recent_quarters) >= 4:
                    break
        
        if len(recent_quarters) >= 4:
            # 计算TTM
            ttm_raw = sum(row['n_income_attr_p'] for row in recent_quarters)
            ttm_billion = ttm_raw / 100000000
            
            print(f"\n📊 净利润TTM计算结果:")
            print(f"  TTM原始值: {ttm_raw:,.0f}")
            print(f"  TTM(亿元): {ttm_billion:.2f}")
            
            # 对比财务指标中的数据
            print(f"\n🔍 对比财务指标中的数据...")
            df_indicator = pro.fina_indicator(**{
                "ts_code": ts_code,
                "end_date": "20240630"
            })
            
            if not df_indicator.empty:
                indicator_row = df_indicator.iloc[0]
                print(f"📋 财务指标数据:")
                for col in df_indicator.columns:
                    if 'income' in col.lower() or 'profit' in col.lower() or 'eps' in col.lower():
                        value = indicator_row[col]
                        if pd.notna(value) and value != 0:
                            print(f"  {col}: {value}")
        else:
            print(f"❌ 数据不足，只找到 {len(recent_quarters)} 个季度")
        
        # 同时测试累计报表的数据
        print(f"\n🔍 对比累计报表数据...")
        df_income_cum = pro.income(**{
            "ts_code": ts_code,
            "start_date": "20230101",
            "end_date": "20241231",
            "report_type": "1"  # 累计合并报表
        }, fields=[
            "ts_code", "end_date", "n_income", "n_income_attr_p", "report_type"
        ])
        
        if not df_income_cum.empty:
            print(f"📋 累计报表净利润数据:")
            df_cum_sorted = df_income_cum.sort_values('end_date', ascending=False)
            
            for _, row in df_cum_sorted.iterrows():
                end_date = row['end_date']
                net_income = row['n_income_attr_p']
                print(f"  {end_date}: {net_income/100000000:.2f} 亿元 (累计)")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    test_net_income_ttm()
