# 调试TTM计算 - 以平安银行为例
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta

# 初始化pro接口
pro = ts.pro_api('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')

def debug_pingan_ttm():
    """
    调试平安银行的TTM计算
    """
    ts_code = "000001.SZ"  # 平安银行
    
    print("🔍 调试平安银行TTM计算")
    print("=" * 60)
    
    try:
        # 获取现金流量表数据
        print("📊 获取现金流量表数据...")
        df_cashflow = pro.cashflow(**{
            "ts_code": ts_code,
            "start_date": "20220101",
            "end_date": "20241231",
            "report_type": "1"
        }, fields=[
            "ts_code", "end_date", "n_cashflow_act", "report_type"
        ])
        
        if df_cashflow.empty:
            print("❌ 未获取到现金流数据")
            return
        
        print(f"✅ 获取到 {len(df_cashflow)} 条现金流数据")
        print("\n📋 现金流数据详情:")
        df_sorted = df_cashflow.sort_values('end_date', ascending=False)
        
        for _, row in df_sorted.iterrows():
            end_date = row['end_date']
            cashflow = row['n_cashflow_act']
            print(f"  {end_date}: {cashflow:,.0f} (原始值)")
            print(f"  {end_date}: {cashflow/100000000:.2f} 亿元")
        
        # 计算2024年Q2的TTM
        print(f"\n🧮 计算2024年Q2的现金流TTM:")
        print(f"公式: Q2现金流TTM = Q2经营现金流净额 - Q1经营现金流净额 + 去年Q2现金流TTM")

        # 查找各期数据
        q2_2024 = None  # 2024年Q2累计
        q1_2024 = None  # 2024年Q1累计
        q2_2023 = None  # 2023年Q2累计
        q1_2023 = None  # 2023年Q1累计
        annual_2022 = None  # 2022年年报

        for _, row in df_sorted.iterrows():
            end_date = str(row['end_date'])
            if end_date == '20240630':
                q2_2024 = row
                print(f"  ✅ 2024年Q2累计: {row['n_cashflow_act']:,.0f}")
            elif end_date == '20240331':
                q1_2024 = row
                print(f"  ✅ 2024年Q1累计: {row['n_cashflow_act']:,.0f}")
            elif end_date == '20230630':
                q2_2023 = row
                print(f"  ✅ 2023年Q2累计: {row['n_cashflow_act']:,.0f}")
            elif end_date == '20230331':
                q1_2023 = row
                print(f"  ✅ 2023年Q1累计: {row['n_cashflow_act']:,.0f}")
            elif end_date == '20221231':
                annual_2022 = row
                print(f"  ✅ 2022年年报: {row['n_cashflow_act']:,.0f}")

        if q2_2024 is not None and q1_2024 is not None and q2_2023 is not None and q1_2023 is not None and annual_2022 is not None:
            # 先计算去年Q2的TTM
            # 去年Q2 TTM = Q2累计 - Q1累计 + 前年年报
            last_year_q2_ttm = q2_2023['n_cashflow_act'] - q1_2023['n_cashflow_act'] + annual_2022['n_cashflow_act']

            # 今年Q2的TTM
            # 今年Q2 TTM = Q2累计 - Q1累计 + 去年Q2 TTM
            current_q2_ttm = q2_2024['n_cashflow_act'] - q1_2024['n_cashflow_act'] + last_year_q2_ttm

            ttm_billion = current_q2_ttm / 100000000  # 转换为亿元

            print(f"\n📊 TTM计算过程:")
            print(f"  步骤1 - 计算去年Q2 TTM:")
            print(f"    2023Q2累计 - 2023Q1累计 + 2022年报")
            print(f"    {q2_2023['n_cashflow_act']:,.0f} - {q1_2023['n_cashflow_act']:,.0f} + {annual_2022['n_cashflow_act']:,.0f}")
            print(f"    = {last_year_q2_ttm:,.0f}")
            print(f"  步骤2 - 计算今年Q2 TTM:")
            print(f"    2024Q2累计 - 2024Q1累计 + 去年Q2TTM")
            print(f"    {q2_2024['n_cashflow_act']:,.0f} - {q1_2024['n_cashflow_act']:,.0f} + {last_year_q2_ttm:,.0f}")
            print(f"    = {current_q2_ttm:,.0f}")
            print(f"  TTM(亿元): {ttm_billion:.2f}")
            print(f"  实际应该是: 1242.96 亿元")
            print(f"  差异: {abs(ttm_billion - 1242.96):.2f} 亿元")
        else:
            print("❌ 数据不完整，无法计算TTM")
        
        # 尝试其他可能的字段
        print(f"\n🔍 检查其他现金流字段...")
        
        # 获取更多字段的现金流数据
        df_cashflow_full = pro.cashflow(**{
            "ts_code": ts_code,
            "start_date": "20230101",
            "end_date": "20241231",
            "report_type": "1"
        })
        
        if not df_cashflow_full.empty:
            print(f"📋 现金流表所有字段:")
            latest_row = df_cashflow_full.iloc[0]
            for col in df_cashflow_full.columns:
                if 'cash' in col.lower() or 'flow' in col.lower():
                    value = latest_row[col]
                    if pd.notna(value) and value != 0:
                        print(f"  {col}: {value:,.0f} ({value/100000000:.2f}亿元)")
        
        # 检查每日基本面数据中的现金流
        print(f"\n🔍 检查每日基本面数据...")
        df_daily_basic = pro.daily_basic(**{
            "ts_code": ts_code,
            "trade_date": "20240630"
        })
        
        if not df_daily_basic.empty:
            print(f"📋 每日基本面数据:")
            for col in df_daily_basic.columns:
                value = df_daily_basic.iloc[0][col]
                if pd.notna(value):
                    print(f"  {col}: {value}")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def check_financial_indicator():
    """
    检查财务指标数据
    """
    ts_code = "000001.SZ"

    print(f"\n🔍 检查财务指标数据...")

    try:
        # 获取财务指标数据
        df_indicator = pro.fina_indicator(**{
            "ts_code": ts_code,
            "start_date": "20230101",
            "end_date": "20241231"
        })

        if not df_indicator.empty:
            print(f"✅ 获取到 {len(df_indicator)} 条财务指标数据")

            # 查看所有财务指标数据
            print(f"📋 所有财务指标数据:")
            df_sorted = df_indicator.sort_values('end_date', ascending=False)

            for _, row in df_sorted.iterrows():
                end_date = row['end_date']
                ocfps = row.get('ocfps', 0)  # 每股经营现金流

                print(f"  {end_date}: 每股经营现金流 = {ocfps}")

                # 如果是2024年Q2，尝试计算总的经营现金流
                if end_date == '20240630':
                    # 需要获取股本数据来计算总现金流
                    print(f"    2024年Q2每股经营现金流: {ocfps}")

                    # 尝试获取股本数据
                    try:
                        df_share = pro.share_float(**{
                            "ts_code": ts_code,
                            "end_date": "20240630"
                        })

                        if not df_share.empty:
                            total_share = df_share.iloc[0]['total_share']  # 总股本(万股)
                            total_cashflow = ocfps * total_share * 10000 / 100000000  # 转换为亿元
                            print(f"    总股本: {total_share} 万股")
                            print(f"    计算的总现金流: {total_cashflow:.2f} 亿元")
                            print(f"    与1242.96的差异: {abs(total_cashflow - 1242.96):.2f} 亿元")
                    except Exception as e:
                        print(f"    获取股本数据失败: {e}")

            # 查看现金流相关字段
            print(f"\n📋 现金流相关字段:")
            latest = df_indicator.iloc[0]
            for col in df_indicator.columns:
                if 'cash' in col.lower() or 'flow' in col.lower() or 'cf' in col.lower() or 'ocf' in col.lower():
                    value = latest[col]
                    if pd.notna(value) and value != 0:
                        print(f"  {col}: {value}")

    except Exception as e:
        print(f"❌ 获取财务指标失败: {e}")

def try_different_calculation():
    """
    尝试不同的TTM计算方法
    """
    ts_code = "000001.SZ"

    print(f"\n🔍 尝试不同的TTM计算方法...")

    try:
        # 获取更详细的现金流数据
        df_cashflow = pro.cashflow(**{
            "ts_code": ts_code,
            "start_date": "20220101",
            "end_date": "20241231",
            "report_type": "2"  # 尝试单季合并报表
        }, fields=[
            "ts_code", "end_date", "n_cashflow_act", "report_type"
        ])

        if not df_cashflow.empty:
            print(f"✅ 获取到单季报表数据 {len(df_cashflow)} 条")
            df_sorted = df_cashflow.sort_values('end_date', ascending=False)

            print(f"📋 单季现金流数据:")
            for _, row in df_sorted.iterrows():
                end_date = row['end_date']
                cashflow = row['n_cashflow_act']
                print(f"  {end_date}: {cashflow:,.0f} ({cashflow/100000000:.2f}亿元)")

            # 尝试用最近4个季度相加
            recent_4_quarters = df_sorted.head(4)
            if len(recent_4_quarters) >= 4:
                total_ttm = recent_4_quarters['n_cashflow_act'].sum()
                ttm_billion = total_ttm / 100000000
                print(f"\n📊 最近4个季度相加TTM: {ttm_billion:.2f} 亿元")
                print(f"与1242.96的差异: {abs(ttm_billion - 1242.96):.2f} 亿元")

    except Exception as e:
        print(f"❌ 获取单季数据失败: {e}")

if __name__ == "__main__":
    debug_pingan_ttm()
    check_financial_indicator()
    try_different_calculation()
