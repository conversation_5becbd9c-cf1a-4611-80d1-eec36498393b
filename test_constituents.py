# 测试按时间范围滚动获取指数成分股
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta
import time

# 初始化pro接口
pro = ts.pro_api('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')

def test_get_constituents_by_date_range(index_code, start_year=2020, end_year=2025):
    """
    按时间范围滚动获取指数成分股
    """
    print(f"🔍 开始测试获取 {index_code} 的成分股数据...")
    
    all_data = []
    
    # 按年份循环
    for year in range(start_year, end_year + 1):
        print(f"\n📅 处理 {year} 年的数据...")
        
        # 按月份循环
        for month in range(1, 13):
            try:
                # 计算该月的第一天和最后一天
                start_date = datetime(year, month, 1)
                if month == 12:
                    end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
                else:
                    end_date = datetime(year, month + 1, 1) - timedelta(days=1)
                
                start_date_str = start_date.strftime("%Y%m%d")
                end_date_str = end_date.strftime("%Y%m%d")
                
                print(f"   📊 获取 {year}-{month:02d} 月数据: {start_date_str} 到 {end_date_str}")
                
                # 调用API
                df = pro.index_weight(**{
                    "index_code": index_code,
                    "trade_date": "",
                    "start_date": int(start_date_str),
                    "end_date": int(end_date_str),
                    "ts_code": "",
                    "limit": "",
                    "offset": ""
                }, fields=[
                    "index_code",
                    "con_code", 
                    "trade_date"
                ])
                
                if not df.empty:
                    print(f"   ✅ 获取到 {len(df)} 条记录")
                    all_data.append(df)
                    
                    # 显示该月的成分股数量
                    unique_stocks = df['con_code'].nunique()
                    print(f"   📋 该月成分股数量: {unique_stocks} 只")
                    
                    # 显示前几只股票作为示例
                    sample_stocks = df['con_code'].unique()[:5]
                    print(f"   📝 示例股票: {', '.join(sample_stocks)}")
                else:
                    print(f"   ⚠️  该月无数据")
                
                # 添加延时避免API限制
                time.sleep(0.5)
                
            except Exception as e:
                print(f"   ❌ 获取 {year}-{month:02d} 月数据失败: {e}")
                continue
    
    # 合并所有数据
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        print(f"\n📈 总计获取数据: {len(combined_df)} 条记录")
        
        # 分析数据
        print(f"📊 数据分析:")
        print(f"   - 时间范围: {combined_df['trade_date'].min()} 到 {combined_df['trade_date'].max()}")
        print(f"   - 总成分股数量: {combined_df['con_code'].nunique()} 只")
        print(f"   - 不同交易日数量: {combined_df['trade_date'].nunique()} 天")
        
        # 显示每个月的成分股数量变化
        print(f"\n📅 各月成分股数量变化:")
        monthly_stats = combined_df.groupby('trade_date')['con_code'].nunique().sort_index()
        for date, count in monthly_stats.head(10).items():
            print(f"   {date}: {count} 只")
        
        if len(monthly_stats) > 10:
            print(f"   ... (还有 {len(monthly_stats) - 10} 个月的数据)")
        
        return combined_df
    else:
        print(f"❌ 未获取到任何数据")
        return pd.DataFrame()

def test_single_month(index_code, year=2024, month=11):
    """
    测试获取单个月的数据
    """
    print(f"🔍 测试获取 {index_code} 在 {year}-{month:02d} 的成分股数据...")
    
    try:
        # 计算该月的第一天和最后一天
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, month + 1, 1) - timedelta(days=1)
        
        start_date_str = start_date.strftime("%Y%m%d")
        end_date_str = end_date.strftime("%Y%m%d")
        
        print(f"📅 日期范围: {start_date_str} 到 {end_date_str}")
        
        # 调用API
        df = pro.index_weight(**{
            "index_code": index_code,
            "trade_date": "",
            "start_date": int(start_date_str),
            "end_date": int(end_date_str),
            "ts_code": "",
            "limit": "",
            "offset": ""
        }, fields=[
            "index_code",
            "con_code", 
            "trade_date"
        ])
        
        if not df.empty:
            print(f"✅ 成功获取 {len(df)} 条记录")
            print(f"📋 成分股数量: {df['con_code'].nunique()} 只")
            print(f"📊 数据预览:")
            print(df.head())
            
            # 转换股票代码格式
            def convert_stock_code_to_format(ts_code):
                if '.' in ts_code:
                    code, exchange = ts_code.split('.')
                    if exchange == 'SZ':
                        return f"sz{code}"
                    elif exchange == 'SH':
                        return f"sh{code}"
                    elif exchange == 'BJ':
                        return f"bj{code}"
                    else:
                        return f"{exchange.lower()}{code}"
                else:
                    return ts_code.lower()
            
            # 转换格式并生成持有股票代码字符串
            stock_codes = []
            unique_codes = df['con_code'].dropna().unique()
            for code in unique_codes:
                formatted_code = convert_stock_code_to_format(str(code))
                stock_codes.append(formatted_code)
            
            stock_codes.sort()
            result = ' '.join(stock_codes)
            print(f"📝 格式化后的持有股票代码 ({len(stock_codes)} 只):")
            print(f"   {result[:200]}..." if len(result) > 200 else f"   {result}")
            
            return df
        else:
            print(f"⚠️  该月无数据")
            return pd.DataFrame()
            
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return pd.DataFrame()

if __name__ == "__main__":
    # 测试不同的指数
    test_indices = [
        "000300.SH",  # 沪深300
        "000016.SH",  # 上证50
        "932000.CSI", # 中证2000
    ]
    
    print("=" * 80)
    print("🚀 开始测试指数成分股获取")
    print("=" * 80)
    
    # 先测试单个月的数据
    for index_code in test_indices:
        print(f"\n{'='*60}")
        print(f"测试指数: {index_code}")
        print(f"{'='*60}")
        
        df = test_single_month(index_code, 2024, 11)
        
        if not df.empty:
            print(f"✅ {index_code} 测试成功")
        else:
            print(f"❌ {index_code} 测试失败")
        
        print("\n" + "-" * 60)
        time.sleep(1)  # 避免API限制
    
    print(f"\n🎉 测试完成!")
