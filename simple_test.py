# 简单测试tushare API调用
print("开始测试...")

try:
    import tushare as ts
    print("✅ tushare导入成功")
except Exception as e:
    print(f"❌ tushare导入失败: {e}")
    exit(1)

try:
    import pandas as pd
    print("✅ pandas导入成功")
except Exception as e:
    print(f"❌ pandas导入失败: {e}")
    exit(1)

try:
    from datetime import datetime, timedelta
    print("✅ datetime导入成功")
except Exception as e:
    print(f"❌ datetime导入失败: {e}")
    exit(1)

# 初始化pro接口
try:
    pro = ts.pro_api('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
    print("✅ tushare pro接口初始化成功")
except Exception as e:
    print(f"❌ tushare pro接口初始化失败: {e}")
    exit(1)

# 测试简单的API调用
print("\n开始测试API调用...")

try:
    # 测试获取指数成分股
    index_code = "000300.SH"
    start_date = 20241101
    end_date = 20241130
    
    print(f"测试参数:")
    print(f"  index_code: {index_code}")
    print(f"  start_date: {start_date}")
    print(f"  end_date: {end_date}")
    
    df = pro.index_weight(**{
        "index_code": index_code,
        "trade_date": "",
        "start_date": start_date,
        "end_date": end_date,
        "ts_code": "",
        "limit": "",
        "offset": ""
    }, fields=[
        "index_code",
        "con_code", 
        "trade_date"
    ])
    
    print(f"✅ API调用成功")
    print(f"📊 返回数据形状: {df.shape}")
    print(f"📋 列名: {list(df.columns)}")
    
    if not df.empty:
        print(f"📝 数据预览:")
        print(df.head())
        
        unique_stocks = df['con_code'].nunique()
        print(f"📈 成分股数量: {unique_stocks} 只")
        
        # 显示前10只股票
        sample_stocks = df['con_code'].unique()[:10]
        print(f"📋 前10只股票: {', '.join(sample_stocks)}")
    else:
        print("⚠️  返回数据为空")

except Exception as e:
    print(f"❌ API调用失败: {e}")
    import traceback
    print(f"详细错误: {traceback.format_exc()}")

print("\n测试完成!")
